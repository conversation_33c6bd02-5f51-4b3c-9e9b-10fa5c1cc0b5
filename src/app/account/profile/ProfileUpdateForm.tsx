"use client";

import placeholderUrl from "@/assets/placeholder.svg";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { authClient } from "@/lib/auth-client";
import { usePageContext } from "@/lib/page-context";
import { Camera, Save, X } from "lucide-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";

export default function ProfileUpdateForm() {
  const { t } = useTranslation();
  const { session } = usePageContext();
  const [userData, setUserData] = useState(session!.user);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const [editedData, setEditedData] = useState(userData);

  const handleInputChange = (field: string, value: string) => {
    setEditedData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSaveProfile = async () => {
    setIsLoading(true);
    try {
      await authClient.updateUser({ name: editedData.name });
      setUserData(editedData);
      setIsEditing(false);
      console.log("Profile updated:", editedData);
    } catch (error) {
      console.error("Error updating profile:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelEdit = () => {
    setEditedData(userData);
    setIsEditing(false);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>{t("Profile Information")}</CardTitle>
            <CardDescription>{t("Update your personal information and profile details")}</CardDescription>
          </div>
          {!isEditing && (
            <Button onClick={() => setIsEditing(true)} variant="outline">
              {t("Edit Profile")}
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Avatar className="h-20 w-20">
              <AvatarImage src={userData.image || placeholderUrl.src} alt={userData.name} />
              <AvatarFallback>
                {userData.name
                  .split(" ")
                  .map((n) => n[0])
                  .join("")}
              </AvatarFallback>
            </Avatar>
            {isEditing && (
              <Button size="sm" variant="secondary" className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full p-0">
                <Camera className="h-4 w-4" />
              </Button>
            )}
          </div>
          <div className="space-y-1">
            <h3 className="text-lg font-semibold">{userData.name}</h3>
            <p className="text-sm text-gray-600">{userData.email}</p>
            {/* <Badge variant="secondary">{userData.plan} Plan</Badge> */}
          </div>
        </div>

        <Separator />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="name">{t("Full Name")}</Label>
            <Input
              id="name"
              value={isEditing ? editedData.name : userData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              disabled={!isEditing}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">{t("Email Address")}</Label>
            <Input
              id="email"
              type="email"
              value={isEditing ? editedData.email : userData.email}
              onChange={(e) => handleInputChange("email", e.target.value)}
              disabled={!isEditing}
            />
          </div>
        </div>

        {isEditing && (
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={handleCancelEdit}>
              <X className="h-4 w-4 mr-2" />
              {t("Cancel")}
            </Button>
            <Button onClick={handleSaveProfile} disabled={isLoading}>
              <Save className="h-4 w-4 mr-2" />
              {isLoading ? "Saving..." : "Save Changes"}
            </Button>
          </div>
        )}

        <Separator />

        <div className="text-sm text-gray-600">
          <p>
            {t("Member since")}
            {userData.createdAt.toDateString()}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
