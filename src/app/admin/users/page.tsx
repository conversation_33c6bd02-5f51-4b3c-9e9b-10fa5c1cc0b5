"use client";

import T from "@/components/T";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { authClient } from "@/lib/auth-client";
import { UserWithRole } from "better-auth/plugins";
import { ChevronLeftIcon, ChevronRightIcon, EditIcon, SaveIcon, XIcon } from "lucide-react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

interface EditingUser {
  id: string;
  role: "user" | "admin";
  password: string;
}

export default function UsersPage() {
  const { t } = useTranslation();
  const router = useRouter();
  const searchParams = useSearchParams();

  const [users, setUsers] = useState<UserWithRole[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(true);
  const [editingUser, setEditingUser] = useState<EditingUser | null>(null);
  const [savingUser, setSavingUser] = useState<string | null>(null);

  // Get current search params
  const currentPage = parseInt(searchParams.get("page") || "1");
  const searchValue = searchParams.get("search") || "";
  const searchField = searchParams.get("searchField") || "email";
  const sortBy = searchParams.get("sortBy") || "createdAt";
  const sortDirection = searchParams.get("sortDirection") || "desc";
  const limit = parseInt(searchParams.get("limit") || "10");

  const offset = (currentPage - 1) * limit;
  const totalPages = Math.ceil(total / limit);

  // Update URL with new search params
  const updateSearchParams = useCallback(
    (updates: Record<string, string | number>) => {
      const params = new URLSearchParams(searchParams.toString());

      Object.entries(updates).forEach(([key, value]) => {
        if (value) {
          params.set(key, value.toString());
        } else {
          params.delete(key);
        }
      });

      // Reset to page 1 when search changes
      if ("search" in updates || "searchField" in updates) {
        params.set("page", "1");
      }

      router.push(`/admin/users?${params.toString()}`);
    },
    [router, searchParams],
  );

  // Fetch users
  const fetchUsers = useCallback(async () => {
    try {
      setLoading(true);

      const query: any = {
        limit,
        offset,
        sortBy,
        sortDirection,
      };

      if (searchValue) {
        query.searchValue = searchValue;
        query.searchField = searchField;
        query.searchOperator = "contains";
      }

      const { data, error } = await authClient.admin.listUsers({ query });

      if (error) {
        toast.error(error.message || "Failed to fetch users");
        return;
      }

      if (data) {
        setUsers(data.users);
        setTotal(data.total);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
      toast.error("Failed to fetch users");
    } finally {
      setLoading(false);
    }
  }, [limit, offset, sortBy, sortDirection, searchValue, searchField]);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  // Handle role update
  const handleRoleUpdate = async (userId: string, newRole: "user" | "admin") => {
    try {
      setSavingUser(userId);

      const { error } = await authClient.admin.setRole({
        userId,
        role: newRole,
      });

      if (error) {
        toast.error(error.message || "Failed to update role");
        return;
      }

      toast.success("Role updated successfully");
      await fetchUsers(); // Refresh the list
      setEditingUser(null);
    } catch (error) {
      console.error("Error updating role:", error);
      toast.error("Failed to update role");
    } finally {
      setSavingUser(null);
    }
  };

  // Handle password update
  const handlePasswordUpdate = async (userId: string, newPassword: string) => {
    if (!newPassword.trim()) {
      toast.error("Password cannot be empty");
      return;
    }

    try {
      setSavingUser(userId);

      const { error } = await authClient.admin.setUserPassword({
        userId,
        newPassword,
      });

      if (error) {
        toast.error(error.message || "Failed to update password");
        return;
      }

      toast.success("Password updated successfully");
      setEditingUser(null);
    } catch (error) {
      console.error("Error updating password:", error);
      toast.error("Failed to update password");
    } finally {
      setSavingUser(null);
    }
  };

  // Handle save changes
  const handleSaveChanges = async () => {
    if (!editingUser) return;

    const { id, role, password } = editingUser;

    // Update role if changed
    const currentUser = users.find((u) => u.id === id);
    if (currentUser && role !== currentUser.role) {
      await handleRoleUpdate(id, role);
    }

    // Update password if provided
    if (password.trim()) {
      await handlePasswordUpdate(id, password);
    }

    if (!role || role === currentUser?.role) {
      setEditingUser(null);
    }
  };

  const startEditing = (user: UserWithRole) => {
    setEditingUser({
      id: user.id,
      role: (user.role as any) || "user",
      password: "",
    });
  };

  const cancelEditing = () => {
    setEditingUser(null);
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            <T text="Users Management" />
          </h1>
          <p className="text-muted-foreground">
            <T text="Manage user accounts, roles, and permissions" />
          </p>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex">
            <T text="Search and Filter" />
            <Button variant="outline" size="sm" asChild className="ms-auto">
              <Link href="/admin/users">
                <T text="Reset" />
              </Link>
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search">
                <T text="Search" />
              </Label>
              <Input
                id="search"
                placeholder="Search users..."
                value={searchValue}
                onChange={(e) => updateSearchParams({ search: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <Label>
                <T text="Search Field" />
              </Label>
              <Select value={searchField} onValueChange={(value) => updateSearchParams({ searchField: value })}>
                <SelectTrigger className="w-full">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="email">{t("Email")}</SelectItem>
                  <SelectItem value="name">{t("Name")}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>
                <T text="Sort By" />
              </Label>
              <Select value={sortBy} onValueChange={(value) => updateSearchParams({ sortBy: value })}>
                <SelectTrigger className="w-full">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="createdAt">{t("Created Date")}</SelectItem>
                  <SelectItem value="name">{t("Name")}</SelectItem>
                  <SelectItem value="email">{t("Email")}</SelectItem>
                  <SelectItem value="role">{t("Role")}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>
                <T text="Sort Direction" />
              </Label>
              <Select value={sortDirection} onValueChange={(value) => updateSearchParams({ sortDirection: value })}>
                <SelectTrigger className="w-full">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="asc">{t("Ascending")}</SelectItem>
                  <SelectItem value="desc">{t("Descending")}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>
              <T text="Users" /> ({total})
            </CardTitle>
            <div className="flex items-center gap-2">
              <Label>
                <T text="Per page:" />
              </Label>
              <Select
                value={limit.toString()}
                onValueChange={(value) => updateSearchParams({ limit: parseInt(value), page: 1 })}
              >
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5</SelectItem>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <T text="Loading..." />
            </div>
          ) : users.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <T text="No users found" />
            </div>
          ) : (
            <div className="space-y-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>
                      <T text="Name" />
                    </TableHead>
                    <TableHead>
                      <T text="Email" />
                    </TableHead>
                    <TableHead>
                      <T text="Role" />
                    </TableHead>
                    <TableHead>
                      <T text="Status" />
                    </TableHead>
                    <TableHead>
                      <T text="Created" />
                    </TableHead>
                    <TableHead>
                      <T text="Actions" />
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">{user.name}</TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>
                        {editingUser?.id === user.id ? (
                          <Select
                            value={editingUser.role}
                            onValueChange={(value) => setEditingUser({ ...editingUser, role: value as any })}
                          >
                            <SelectTrigger className="w-32">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="user">{t("User")}</SelectItem>
                              <SelectItem value="admin">{t("Admin")}</SelectItem>
                            </SelectContent>
                          </Select>
                        ) : (
                          <Badge variant={user.role === "admin" ? "default" : "secondary"}>{user.role}</Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        {user.banned ? (
                          <Badge variant="destructive">{t("Banned")}</Badge>
                        ) : (
                          <Badge variant="outline">{t("Active")}</Badge>
                        )}
                      </TableCell>
                      <TableCell>{new Date(user.createdAt).toLocaleDateString()}</TableCell>
                      <TableCell>
                        {editingUser?.id === user.id ? (
                          <div className="flex items-center gap-2">
                            {/* <Input
                              type="password"
                              placeholder="New password (optional)"
                              value={editingUser.password}
                              onChange={(e) => setEditingUser({ ...editingUser, password: e.target.value })}
                              className="w-40"
                            /> */}
                            <Button size="sm" onClick={handleSaveChanges} disabled={savingUser === user.id}>
                              <SaveIcon className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={cancelEditing}
                              disabled={savingUser === user.id}
                            >
                              <XIcon className="h-4 w-4" />
                            </Button>
                          </div>
                        ) : (
                          <Button
                            type="button"
                            size="sm"
                            variant="outline"
                            onClick={() => startEditing(user)}
                            disabled={!!editingUser}
                          >
                            <EditIcon className="h-4 w-4" />
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    <T text="Showing" /> {offset + 1} <T text="to" /> {Math.min(offset + limit, total)} <T text="of" />{" "}
                    {total} <T text="users" />
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => updateSearchParams({ page: currentPage - 1 })}
                      disabled={currentPage <= 1}
                    >
                      <ChevronLeftIcon className="h-4 w-4" />
                      <T text="Previous" />
                    </Button>

                    <div className="flex items-center gap-1">
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        let pageNum;
                        if (totalPages <= 5) {
                          pageNum = i + 1;
                        } else if (currentPage <= 3) {
                          pageNum = i + 1;
                        } else if (currentPage >= totalPages - 2) {
                          pageNum = totalPages - 4 + i;
                        } else {
                          pageNum = currentPage - 2 + i;
                        }

                        return (
                          <Button
                            key={pageNum}
                            variant={currentPage === pageNum ? "default" : "outline"}
                            size="sm"
                            onClick={() => updateSearchParams({ page: pageNum })}
                          >
                            {pageNum}
                          </Button>
                        );
                      })}
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => updateSearchParams({ page: currentPage + 1 })}
                      disabled={currentPage >= totalPages}
                    >
                      <T text="Next" />
                      <ChevronRightIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
