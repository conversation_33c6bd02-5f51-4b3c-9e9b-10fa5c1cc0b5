"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { PromptItem } from "@/database/drizzle/schema/prompts";
import { prompts } from "@/lib/prompts";
import { ArrowLeftIcon, RotateCcwIcon, SaveIcon } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState, useTransition } from "react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import { onResetPrompt, onUpdatePrompt } from "./actions";

interface Props {
  id: keyof typeof prompts;
  prompt: PromptItem;
  defaultPrompt: Omit<(typeof prompts)[keyof typeof prompts], "schema">;
}

export default function PromptEditForm({ id, prompt, defaultPrompt }: Props) {
  const { t } = useTranslation();
  const [isPending, startTransition] = useTransition();
  const [isResetting, setIsResetting] = useState(false);
  const router = useRouter();

  const [formData, setFormData] = useState({
    model: prompt.model,
    prompt: prompt.prompt,
    maxTokens: prompt.maxTokens,
  });

  const hasChanges =
    formData.model !== prompt.model || formData.prompt !== prompt.prompt || formData.maxTokens !== prompt.maxTokens;

  const isDefault =
    formData.model === defaultPrompt.model &&
    formData.prompt === defaultPrompt.prompt &&
    formData.maxTokens === defaultPrompt.maxTokens;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const form = new FormData();
    form.append("model", formData.model);
    form.append("prompt", formData.prompt);
    form.append("maxTokens", formData.maxTokens.toString());

    startTransition(async () => {
      const result = await onUpdatePrompt(id, form);
      if (result?.error) {
        toast.error(result.error);
      } else {
        toast.success("Prompt updated successfully");
      }
    });
  };

  const handleReset = async () => {
    setIsResetting(true);
    try {
      const result = await onResetPrompt(id);
      if (result?.error) {
        toast.error(result.error);
      } else {
        setFormData({
          model: defaultPrompt.model,
          prompt: defaultPrompt.prompt,
          maxTokens: defaultPrompt.maxTokens,
        });
        toast.success("Prompt reset to default values");
        router.refresh();
      }
    } finally {
      setIsResetting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex-1">
        <h1 className="text-3xl font-bold">
          {t("Edit")}
          {id}
        </h1>
        <p className="text-muted-foreground">{t("Modify prompt configuration and template")}</p>
      </div>
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" asChild>
          <Link href="/admin/prompts">
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            {t("Back to Prompts")}
          </Link>
        </Button>
        {!isDefault && (
          <Button variant="outline" onClick={handleReset} disabled={isResetting}>
            <RotateCcwIcon className="h-4 w-4 mr-2" />
            {t("Reset to Default")}
          </Button>
        )}
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>{t("Configuration")}</CardTitle>
            <CardDescription>{t("Basic prompt settings and metadata")}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>{t("Model")}</Label>
                <Select
                  value={formData.model}
                  onValueChange={(value) => setFormData((prev) => ({ ...prev, model: value }))}
                  required
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {["sonar", "sonar-pro", "sonar-deep-research", "sonar-reasoning", "sonar-reasoning-pro"].map(
                      (x) => (
                        <SelectItem key={x} value={x}>
                          {x}
                        </SelectItem>
                      ),
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="maxTokens">{t("Max Tokens")}</Label>
                <Input
                  id="maxTokens"
                  type="number"
                  min="1"
                  max="100000"
                  value={formData.maxTokens}
                  onChange={(e) => setFormData((prev) => ({ ...prev, maxTokens: parseInt(e.target.value) || 0 }))}
                  required
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t("Prompt Template")}</CardTitle>
            <CardDescription>
              {t("The template used for generating AI prompts. Use Handlebars syntax for parameters.")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="prompt">{t("Template")}</Label>
              <Textarea
                id="prompt"
                value={formData.prompt}
                onChange={(e) => setFormData((prev) => ({ ...prev, prompt: e.target.value }))}
                placeholder={t("Enter your prompt template...")}
                className="min-h-[200px] font-mono"
                required
              />
            </div>
          </CardContent>
        </Card>

        {defaultPrompt.paramDescriptions && Object.keys(defaultPrompt.paramDescriptions).length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>{t("Available Parameters")}</CardTitle>
              <CardDescription>{t("Parameters that can be used in the template")}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {Object.entries(defaultPrompt.paramDescriptions).map(([param, description]) => (
                  <div key={param} className="border-l-4 border-primary/20 pl-4">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge variant="outline" className="font-mono text-xs">
                        {`{{${param}}}`}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">{description}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        <Separator />

        <div className="flex justify-end gap-3">
          <Button type="button" variant="outline" asChild>
            <Link href={`/admin/prompts/${id}`}>{t("Cancel")}</Link>
          </Button>
          <Button type="submit" disabled={!hasChanges || isPending}>
            <SaveIcon className="h-4 w-4 mr-2" />
            {isPending ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      </form>
    </div>
  );
}
