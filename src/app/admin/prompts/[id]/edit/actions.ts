"use server";

import { getOrCreatePrompt, updatePrompt } from "@/database/drizzle/queries/prompts";
import { errors } from "@/lib/errors";
import { getDataContext } from "@/lib/getDataContext";
import { prompts } from "@/lib/prompts";
import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import z from "zod";

const promptUpdateSchema = z.object({
  model: z.string().min(1, "Model is required"),
  prompt: z.string().min(1, "Prompt is required"),
  maxTokens: z.number().min(1, "Max tokens must be at least 1").max(100000, "Max tokens cannot exceed 100,000"),
});

export async function onGetPrompt(id: keyof typeof prompts) {
  const context = await getDataContext();

  if (!context.session?.user) return { error: errors.NotAuthenticated };
  if (context.session.user.role !== "admin") return { error: errors.NotAuthorized };

  if (!(id in prompts)) {
    return { error: "Prompt not found" };
  }

  try {
    const prompt = await getOrCreatePrompt(context.db, id);
    return { prompt };
  } catch (error) {
    console.error("Error getting prompt:", error);
    return { error: "Failed to get prompt" };
  }
}

export async function onUpdatePrompt(id: keyof typeof prompts, formData: FormData) {
  const context = await getDataContext();

  if (!context.session?.user) return { error: errors.NotAuthenticated };
  if (context.session.user.role !== "admin") return { error: errors.NotAuthorized };

  if (!(id in prompts)) {
    return { error: "Prompt not found" };
  }

  try {
    const data = {
      model: formData.get("model") as string,
      prompt: formData.get("prompt") as string,
      maxTokens: parseInt(formData.get("maxTokens") as string),
    };

    const validatedData = promptUpdateSchema.parse(data);

    await updatePrompt(context.db, id, validatedData);

    revalidatePath(`/admin/prompts/${id}`);
    revalidatePath(`/admin/prompts/${id}/edit`);
    revalidatePath("/admin/prompts");

    redirect(`/admin/prompts/${id}`);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { error: error.issues[0].message };
    }
    console.error("Error updating prompt:", error);
    return { error: "Failed to update prompt" };
  }
}

export async function onResetPrompt(id: keyof typeof prompts) {
  const context = await getDataContext();

  if (!context.session?.user) return { error: errors.NotAuthenticated };
  if (context.session.user.role !== "admin") return { error: errors.NotAuthorized };

  if (!(id in prompts)) {
    return { error: "Prompt not found" };
  }

  try {
    const defaultPrompt = prompts[id];

    await updatePrompt(context.db, id, {
      model: defaultPrompt.model,
      prompt: defaultPrompt.prompt,
      maxTokens: defaultPrompt.maxTokens,
    });

    revalidatePath(`/admin/prompts/${id}`);
    revalidatePath(`/admin/prompts/${id}/edit`);
    revalidatePath("/admin/prompts");

    return { success: true };
  } catch (error) {
    console.error("Error resetting prompt:", error);
    return { error: "Failed to reset prompt" };
  }
}
