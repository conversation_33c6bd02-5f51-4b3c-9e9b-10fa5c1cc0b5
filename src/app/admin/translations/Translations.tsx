"use client";

import { Field } from "@/components/Field";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger } from "@/components/ui/dialog";
import { Form } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { matchSorter } from "match-sorter";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { z } from "zod";
import { useData } from "./data.client";

export function Translations() {
  const data = useData();
  const { t } = useTranslation();
  const [translations, setTranslations] = useState(
    data.translations
      ? Array.from(Object.keys(data.translations.en.translation), (x) => ({
          id: x,
          en: data.translations!.en.translation[x],
          fr: data.translations!.fr.translation[x],
        }))
      : [],
  );
  const [searchTerm, setSearchTerm] = useState("");
  const results = searchTerm
    ? matchSorter(translations, searchTerm, { keys: ["en", "fr"] })
    : translations.slice(0, 10);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <h1 className="text-3xl font-bold">{t("Translations")}</h1>
      <Input placeholder={t("Search")} value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} />
      {results.map((x) => (
        <EditTranslation x={x} key={x.id} />
      ))}
    </div>
  );
}

const translationSchema = z.object({
  en: z.string(),
  fr: z.string(),
});

function EditTranslation({ x }: { x: { id: string; en: string; fr: string } }) {
  const { t } = useTranslation();
  const form = useForm<z.infer<typeof translationSchema>>({
    resolver: zodResolver(translationSchema),
    defaultValues: {
      en: x.en,
      fr: x.fr,
    },
  });

  const onSubmit = (data: z.infer<typeof translationSchema>) => {
    // TODO: Implement save functionality
    console.log("Saving translation:", { id: x.id, ...data });
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline">{x.id}</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t("Edit Translation")}</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 p-4">
            <Field name="en" label={t("English")} control={form.control} />
            <Field name="fr" label={t("French")} control={form.control} />
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="outline">
                  {t("Cancel")}
                </Button>
              </DialogClose>
              <DialogClose asChild>
                <Button type="submit">{t("Save")}</Button>
              </DialogClose>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
