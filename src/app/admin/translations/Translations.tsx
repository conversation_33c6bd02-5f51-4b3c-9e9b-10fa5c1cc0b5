"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { matchSorter } from "match-sorter";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useData } from "./data.client";

export function Translations() {
  const data = useData();
  const { t } = useTranslation();
  const [translations, setTranslations] = useState(
    data.translations
      ? Array.from(Object.keys(data.translations.en.translation), (x) => ({
          id: x,
          en: data.translations!.en.translation[x],
          fr: data.translations!.fr.translation[x],
        }))
      : [],
  );
  const [searchTerm, setSearchTerm] = useState("");
  const results = searchTerm
    ? matchSorter(translations, searchTerm, { keys: ["en", "fr"] })
    : translations.slice(0, 10);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <h1 className="text-3xl font-bold">{t("Translations")}</h1>
      <Input placeholder={t("Search")} value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} />
      {results.map((x) => (
        <EditTranslation x={x} key={x.id} />
      ))}
    </div>
  );
}

function EditTranslation({ x }: { x: { id: string; en: string; fr: string } }) {
  const { t } = useTranslation();
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline">{x.id}</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t("Edit Translation")}</DialogTitle>
        </DialogHeader>
        {/* <Form {...form}>
          <form onSubmit={form.handleSubmit()} className="space-y-4 p-4">
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="outline">
                  {t("Cancel")}
                </Button>
              </DialogClose>
              <DialogClose asChild>
                <Button type="submit">{t("Save")}</Button>
              </DialogClose>
            </DialogFooter>
          </form>
        </Form> */}
      </DialogContent>
    </Dialog>
  );
}
