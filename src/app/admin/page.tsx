import T from "@/components/T";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { FileBoxIcon, UsersIcon } from "lucide-react";
import Link from "next/link";

export default function Page() {
  return (
    <div className="grid gap-6 grid-cols-[repeat(auto-fill,minmax(300px,1fr))]">
      <Card>
        <CardHeader>
          <div className="flex items-center gap-4">
            <FileBoxIcon className="h-12 w-12 text-muted-foreground" />
            <CardTitle>
              <T text="Prompts" />
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <p>
            <T text="Manage AI prompts used throughout the application" />
          </p>
        </CardContent>
        <CardFooter>
          <Button asChild variant={"outline"}>
            <Link href="/admin/prompts">
              <T text="View" />
            </Link>
          </Button>
        </CardFooter>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex items-center gap-4">
            <UsersIcon className="h-12 w-12 text-muted-foreground" />
            <CardTitle>
              <T text="Users" />
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <p>
            <T text="Manage user accounts, roles, and permissions" />
          </p>
        </CardContent>
        <CardFooter>
          <Button asChild variant={"outline"}>
            <Link href="/admin/users">
              <T text="View" />
            </Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
