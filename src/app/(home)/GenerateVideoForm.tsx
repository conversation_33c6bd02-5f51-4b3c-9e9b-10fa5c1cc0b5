"use client";

import { Field } from "@/components/Field";
import { But<PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { usePageContext } from "@/lib/page-context";
import { tf } from "@/lib/tf";
import { cn } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowDownIcon, ArrowUpIcon, Loader2Icon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import { z } from "zod";
import { onGetTargetSuggestion, onGetVideoTitles, onSaveVideoIdea } from "./GenerateVideoForm.action";

const formSchema = z.object({
  theme: z.string().nonempty(),
  target: z.string().nonempty(),
  frequency: z.coerce.number().min(1).max(5) as z.ZodNumber,
  days: z.coerce.number().min(1).max(5) as z.ZodNumber,
  videos: z.array(z.object({ title: z.string() })),
});

const defaultValues = {
  theme: "",
  target: "",
  frequency: 1,
  days: 1,
  videos: [],
};

export function GenerateVideoForm() {
  const { t } = useTranslation();
  const { session } = usePageContext();
  const router = useRouter();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues,
  });
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isTargetLoading, setIsTargetLoading] = useState(false);

  const { fields, swap } = useFieldArray({
    control: form.control,
    name: "videos",
  });

  useEffect(() => {
    const videoIdea = localStorage.getItem("videoIdea");
    if (videoIdea) {
      form.reset(JSON.parse(videoIdea));
    }
  }, [form]);

  async function onSubmit(values: z.infer<typeof formSchema>) {
    if (isLoading) return;
    setIsLoading(true);
    if (!values.videos.length) {
      const res = await tf(onGetVideoTitles, values);
      if (!res?.titles.length) return;
      form.setValue(
        "videos",
        res.titles.map((x) => ({ title: x })),
      );
    } else if (session?.user.id) {
      await tf(onSaveVideoIdea, values);

      form.reset();
      localStorage.removeItem("videoIdea");
      toast.success(t("Started video generation"));
    } else {
      localStorage.setItem("videoIdea", JSON.stringify(values));
      router.push("/login?onboarding=true");
    }
    setIsLoading(false);
  }

  return (
    <>
      <div className="mt-8">
        <h2 className="mb-4 text-2xl font-bold">{t("Generate videos")}</h2>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {!form.watch("videos").length ? (
              <>
                <Field
                  control={form.control}
                  name="theme"
                  label={t("Theme")}
                  input={{
                    props: {
                      placeholder: t("e.g. promo video or tutorial"),
                      onBlur: async (e) => {
                        if (!e.target.value) return;
                        setIsTargetLoading(true);
                        const res = await tf(onGetTargetSuggestion, e.target.value);
                        if (res) setSuggestions(res.suggestions);
                        setIsTargetLoading(false);
                      },
                    },
                  }}
                />
                <Field control={form.control} name="target" label={t("Target")} input={{ tag: Textarea }} />
                <div className="flex gap-2 flex-wrap items-center">
                  {isTargetLoading && <Loader2Icon className="animate-spin" />}
                  {suggestions.map((x) => (
                    <Button
                      type="button"
                      key={x}
                      variant="outline"
                      disabled={form.watch("target") === x}
                      onClick={() => form.setValue("target", x)}
                    >
                      {x}
                    </Button>
                  ))}
                </div>
                <Field
                  control={form.control}
                  name="frequency"
                  label={t("Videos per day")}
                  input={{ props: { type: "number", step: 1, min: 1, max: 5 } }}
                />
                <Field
                  control={form.control}
                  name="days"
                  label={t("No of days")}
                  input={{ props: { type: "number", step: 1, min: 1, max: 5 } }}
                />
              </>
            ) : (
              <>
                {fields.map((field, index) => (
                  <div key={field.id} className={cn("flex gap-2 p-2 rounded-2xl")}>
                    <div className="space-y-2 flex-1">
                      <h2 className="text-lg font-bold">
                        {t("Day")} {Math.floor(index / form.watch("frequency")) + 1} {t("Video")}{" "}
                        {(index % form.watch("frequency")) + 1}
                      </h2>
                      <Field name={`videos.${index}.title`} label={t("Title")} control={form.control} />
                      <div className="flex gap-2">
                        <Button
                          type="button"
                          onClick={() => swap(index - 1, index)}
                          variant="outline"
                          disabled={index === 0}
                        >
                          <ArrowUpIcon />
                        </Button>
                        <Button
                          type="button"
                          onClick={() => swap(index + 1, index)}
                          variant="outline"
                          disabled={index === fields.length - 1}
                        >
                          <ArrowDownIcon />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
                <Button type="button" variant="destructive" onClick={() => form.reset(defaultValues)}>
                  {t("Reset")}
                </Button>
              </>
            )}
            <hr />
            <Button type="submit" loading={isLoading} disabled={isLoading}>
              {t("Generate")}
            </Button>
          </form>
        </Form>
      </div>
    </>
  );
}
