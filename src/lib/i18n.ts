import translations from "@/assets/translations.json";
import i18n from "i18next";
import { initReactI18next } from "react-i18next";

// the translations
// (tip move them in a JSON file and import them,
// or even better, manage them separated from your code: https://react.i18next.com/guides/multiple-translation-files)
export const resources = translations;

export function initI18n(lang: string) {
  i18n
    .use(initReactI18next) // passes i18n down to react-i18next
    .init({
      resources,
      lng: lang || "en",

      interpolation: {
        escapeValue: false, // react already safes from xss
      },
    });

  return i18n;
}
