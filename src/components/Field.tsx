import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { ComponentProps, ReactNode } from "react";
import { ControllerProps, FieldPath, FieldValues, useController } from "react-hook-form";
import { Checkbox } from "./ui/checkbox";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "./ui/form";
import { Input } from "./ui/input";
import { Textarea } from "./ui/textarea";

export function Field<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
  I extends typeof Input | typeof Textarea = typeof Input,
>({
  label,
  input,
  ...props
}: Omit<ControllerProps<TFieldValues, TName>, "render"> & {
  label: string;
  input?: {
    tag?: I;
    props?: Omit<ComponentProps<I>, "ref">;
  };
}) {
  const InputTag = input?.tag ?? Input;
  return (
    <FormField
      {...props}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <InputTag placeholder={label} {...field} {...(input?.props as any)} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export function RichField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  label,
  // input,
  ...props
}: Omit<ControllerProps<TFieldValues, TName>, "render"> & {
  label: string;
  input?: {
    props?: Omit<ComponentProps<"div">, "ref">;
  };
}) {
  const { field } = useController({
    control: props.control,
    name: props.name,
  });
  const editor = useEditor({
    extensions: [StarterKit], // define your extension array
    content: field.value,
    onUpdate: (event) => {
      field.onChange(event.editor.getHTML());
    },
    immediatelyRender: false,
  });
  return (
    <FormField
      {...props}
      render={(
        {
          // field
        },
      ) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <EditorContent editor={editor} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export function SelectField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  label,
  options,
  ...props
}: Omit<ControllerProps<TFieldValues, TName>, "render"> & { label: string; options: { label: string; value: any }[] }) {
  return (
    <FormField
      {...props}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <Select {...field} onValueChange={field.onChange}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder={label} />
              </SelectTrigger>
              <SelectContent>
                {options.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export function CheckboxField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({ label, ...props }: Omit<ControllerProps<TFieldValues, TName>, "render"> & { label: ReactNode }) {
  return (
    <FormField
      {...props}
      render={({ field }) => (
        <FormItem className="flex flex-row items-center gap-2">
          <FormControl>
            <Checkbox {...field} checked={field.value} onCheckedChange={field.onChange} />
          </FormControl>
          <FormLabel className="text-sm font-normal">{label}</FormLabel>
        </FormItem>
      )}
    />
  );
}
