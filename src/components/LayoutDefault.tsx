"use client";

import placeholderUrl from "@/assets/placeholder.svg";
import { Notifications } from "@/components/Notifications";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { authClient } from "@/lib/auth-client";
import { usePageContext } from "@/lib/page-context";
import { cn } from "@/lib/utils";
import Cookies from "js-cookie";
import { FileBoxIcon, LogInIcon, MenuIcon, UsersIcon, Video } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useTranslation } from "react-i18next";
import z from "zod";
import { en, fr } from "zod/locales";
import { She<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>etHeader, <PERSON>et<PERSON><PERSON><PERSON>, She<PERSON>Trigger } from "./ui/sheet";

export default function LayoutDefault({ children }: { children: React.ReactNode }) {
  return (
    <div className={"flex flex-col h-screen"}>
      <div className="flex flex-col flex-grow">
        <TopBar />
        <Content>{children}</Content>
      </div>
    </div>
  );
}

function NavLink({ children, ...props }: { children: React.ReactNode } & React.ComponentProps<typeof Link>) {
  const urlPathname = usePathname();

  return (
    <Link
      {...props}
      className={cn(
        "flex items-center gap-2 px-3 py-2 rounded-xl",
        props.className,
        urlPathname.startsWith(props.href.toString()) && "bg-gray-500/20",
      )}
    >
      {children}
    </Link>
  );
}

function TopBar() {
  const { t, i18n } = useTranslation();
  const { session } = usePageContext();

  let menu;
  let iconsMenu;
  if (session?.user.role === "admin") {
    menu = (
      <>
        <NavLink href="/admin/prompts">
          <FileBoxIcon />
          {t("Prompts")}
        </NavLink>
        <NavLink href="/admin/users">
          <UsersIcon />
          {t("Users")}
        </NavLink>
      </>
    );
    iconsMenu = (
      <>
        <Notifications />
        <DropdownMenu>
          <DropdownMenuTrigger>
            <Avatar className="h-10 w-10">
              <AvatarImage src={session.user.image || placeholderUrl.src} alt={session.user.name} />
              <AvatarFallback>{session.user.name[0]}</AvatarFallback>
            </Avatar>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>{session.user.email}</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link href="/admin/account">{t("Account")}</Link>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={async () => {
                await authClient.signOut();
                window.location.reload();
              }}
            >
              {t("logout")}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </>
    );
  } else if (session?.user) {
    menu = (
      <>
        <Link href="/videos" className="flex items-center gap-2">
          <Video />
          {t("Videos")}
        </Link>
      </>
    );
    iconsMenu = (
      <>
        <Notifications />
        <DropdownMenu>
          <DropdownMenuTrigger>
            <Avatar className="h-10 w-10">
              <AvatarImage src={session.user.image || placeholderUrl.src} alt={session.user.name} />
              <AvatarFallback>{session.user.name[0]}</AvatarFallback>
            </Avatar>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>{session.user.email}</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link href="/account/profile">{t("Account")}</Link>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={async () => {
                await authClient.signOut();
                localStorage.clear();
                window.location.reload();
              }}
            >
              {t("logout")}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </>
    );
  } else {
    menu = (
      <Link href="/login" className="flex items-center gap-2">
        <LogInIcon />
        {t("Login")}
      </Link>
    );
  }
  const langSelector = (
    <Select
      value={i18n.resolvedLanguage || "fr"}
      onValueChange={(lang) => {
        i18n.changeLanguage(lang);
        Cookies.set("lang", lang);
        z.config(lang === "en" ? en() : fr());
      }}
    >
      <SelectTrigger className="max-md:w-full">
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="en">EN 🇬🇧</SelectItem>
        <SelectItem value="fr">FR 🇫🇷</SelectItem>
      </SelectContent>
    </Select>
  );

  return (
    <div className="bg-gray-50">
      <div className="max-w-5xl m-auto px-5 py-3 flex items-center gap-4 relative">
        <Link href="/" className="text-3xl me-auto">
          Shorts
        </Link>
        <div className={cn("flex items-center gap-4 max-md:hidden")}>
          {menu}
          {iconsMenu}
          {langSelector}
        </div>
        <div className={cn("flex items-center gap-4 md:hidden")}>
          {iconsMenu}
          <Sheet>
            <SheetTrigger>
              <MenuIcon />
            </SheetTrigger>
            <SheetContent side="left">
              <SheetHeader>
                <SheetTitle>{t("Menu")}</SheetTitle>
              </SheetHeader>
              <div className="flex flex-col gap-4 p-2 items-stretch">
                {langSelector}
                {menu}
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </div>
  );
}

function Content({ children }: { children: React.ReactNode }) {
  const urlPathname = usePathname();
  return (
    <div id="page-container" className={cn("overflow-auto", urlPathname === "/login" ? "my-auto " : "flex-1")}>
      <div id="page-content" className={"max-w-5xl m-auto p-5 pb-12"}>
        {children}
      </div>
    </div>
  );
}
